import os
import numpy as np
import pandas as pd
from arcticdb import Arctic
from main.tanki import Tanki


def combine_with_cons_volume(sym_id):
    if os.path.exists(f"/home/<USER>/repos/data_auditing/success_combine_with_cons_volume/{sym_id}/"):
        return
    try:
        print(f"Started for {sym_id}")
        storea = Arctic(
            "s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret"
        )
        storek = Arctic(
            "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )
        lib = storea["nse/1_min/optstk_id_wise/ord"]
        df = lib.read(sym_id, date_range=(None, pd.Timestamp(2025, 6, 24))).data

        dftrd = pd.DataFrame()
        if os.path.exists(
            f"/home/<USER>/repos/data_auditing/optstk_trd_combined_id_wise/{sym_id}.parquet"
        ):
            dftrd = pd.read_parquet(
                f"/home/<USER>/repos/data_auditing/optstk_trd_combined_id_wise/{sym_id}.parquet"
            )
            dftrd = dftrd[dftrd.index < pd.Timestamp(2025, 6, 24)]
            df = pd.merge(df, dftrd, on=["timestamp", "ID"], how="left")
        else:
            df["Cons_Volume"] = np.nan

        df.Cons_Volume = df.groupby([df.index.date, "ID"])["Cons_Volume"].ffill()

        df = df[
            [
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "sym_id",
                "strike",
                "option_type",
                "expiry_date",
                "OI",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
            ]
        ]

        storek = Arctic(
            "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )
        libk = storek["nse/1_min/optstk/ord"]

        df.strike *= 100
        df.sym_id = df.sym_id.astype("int32")
        
        dfold=pd.DataFrame()
        if libk.has_symbol(sym_id):
            dfold = libk.read(sym_id, date_range=(pd.Timestamp(2025, 6, 24), None)).data
        elif os.path.exists(f"/home/<USER>/repos/data_auditing/optstk_ord_backup/{sym_id}.parquet"):
            dfold = pd.read_parquet(f"/home/<USER>/repos/data_auditing/optstk_ord_backup/{sym_id}.parquet")
            
        dfold.to_parquet(
            f"/home/<USER>/repos/data_auditing/optstk_ord_backup/{sym_id}.parquet"
        )

        df = pd.concat([df, dfold])
        
        df = df.reset_index().drop_duplicates(subset=['timestamp', 'ID']).set_index('timestamp')
        
        df.OI = df.groupby("ID").OI.ffill()

        libk.delete(sym_id)
        tanki = Tanki(exchange_type="nse", write=True)
        tanki.login(username="airflow", password="airflow")
        tanki["nse/1_min/optstk/ord"].write_metadata(sym_id, df)
        tanki["nse/1_min/optstk/ord"].write(sym_id, df)

        os.remove(
            f"/home/<USER>/repos/data_auditing/optstk_ord_backup/{sym_id}.parquet"
        )
        
        os.makedirs(
            f"/home/<USER>/repos/data_auditing/success_combine_with_cons_volume/{sym_id}/",
            exist_ok=True,
        )

        print(f"Done for {sym_id}")
    except Exception as e:
        storek["nse/1_min/optstk/ord"].delete(sym_id)
        os.makedirs(
            f"/home/<USER>/repos/data_auditing/failed_combine_with_cons_volume/{sym_id}/",
            exist_ok=True,
        )
        with open(
            f"/home/<USER>/repos/data_auditing/failed_combine_with_cons_volume/{sym_id}/error.txt",
            "w",
        ) as f:
            f.write(f"Failed for {sym_id} due to: {e}\n")
        print(f"Failed for {sym_id} due to: {e}\n")


# combine_with_cons_volume(sym_id="972")


import multiprocessing
import pickle

with open("final_ids_213", "rb") as f:
    ids = pickle.load(f)

with multiprocessing.Pool(2) as p:
    p.map(combine_with_cons_volume, ids)

