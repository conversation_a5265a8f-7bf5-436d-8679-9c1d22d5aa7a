from io import BytesIO
import pickle
import pandas as pd
from arcticdb import Arctic
from minio import Minio


storek = Arctic(
    "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
)


MINIO_END_POINT_219 = "192.168.0.198:11009"
MINIO_ACCESS_KEY = "minioreader"
MINIO_SECRET_KEY = "reasersecret"
minio_client_143 = Minio(
    MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
)

symbol_to_balte_id = pickle.loads(
    minio_client_143.get_object(
        "commondata",
        "balte_uploads/mapping_dict",
    ).data
)


def add_demerger_merger(symbols_exdate):
    dm = storek["nse/1440_min/after_market/trd"].read("demerger_merger").data
    symbols_csv = pd.read_csv(
        minio_client_143.get_object("commondata", "support_files/symbols.csv"),
        header=None,
    )
    symbols_csv.columns = ["symbol", "ID"]

    for symbol, exdate in symbols_exdate:
        dm = pd.concat(
            [
                dm,
                pd.DataFrame(
                    [[symbol, symbol_to_balte_id[symbol]]],
                    columns=dm.columns,
                    index=[exdate],
                ).astype(dm.dtypes),
            ]
        )
        symbols_csv["symbol"] = symbols_csv["symbol"].replace(
            {symbol: f"{symbol}_{exdate.strftime('%d%b%Y')}"}
        )
        symbols_csv = pd.concat(
            [
                symbols_csv,
                pd.DataFrame(
                    [
                        [
                            symbol,
                            symbols_csv.loc[symbols_csv.ID < 5000, "ID"].max() + 1,
                        ]
                    ],
                    columns=symbols_csv.columns,
                ),
            ]
        ).reset_index(drop=True)

        dm.index.name = "exdate"
    dm.to_parquet(
        f"new_demerger_merger_{pd.Timestamp.today().normalize().strftime('%Y%m%d')}.parquet"
    )
    symbols_csv.to_csv(
        f"new_symbols_csv_{pd.Timestamp.today().normalize().strftime('%Y%m%d')}.csv",
        index=False,
        header=None,
    )

    print()


add_demerger_merger(
    symbols_exdate=[
        ("IWEL", pd.Timestamp(2025, 6, 20)),
        ("UMANGDAIRY", pd.Timestamp(2025, 6, 27)),
    ]
)
