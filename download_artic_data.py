import pandas as pd
import os
from arcticdb import Arctic

store = Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")

# Choose the library
library_name = "mcx/compilation"  # Change this to your actual library
library = store[library_name]
symbols = library.list_symbols()
symbols = [s for s in symbols if "20250220_20250326" in s]
# print()
# # Define local storage path
output_folder = "last_arcticdl"
# os.makedirs(output_folder, exist_ok=True)

# # List all symbols in the library
# symbols = library.list_symbols()

# # Download and save each symbol
for symbol in symbols:
    try:
        # Read data from Arctic
        df = library.read(symbol).data

        # Define file path
        file_path = os.path.join(output_folder, f"{symbol}.parquet")

        # Save DataFrame as CSV
        df.to_parquet(file_path)

        print(f"Saved: {file_path}")

    except Exception as e:
        print(f"Error downloading {symbol}: {e}")

print("All symbols downloaded successfully.")


# # # import tarfile


# # # with tarfile.open("arctic_downloads.tar.gz", "w:gz") as tar:
# # #     tar.add("arctic_downloads", arcname=".")

# # # print("Compression complete: your_folder.tar.gz")


# # import pandas as pd
# # # import numpy as np
# # # df = np.load('/home/<USER>/repos/data_auditing/mcx_data/natural_gas_expiry_dict',allow_pickle=True)
# # # df = pd.DataFrame.from_dict(df, orient='index')
# # # df.to_parquet('mcx_data/natural_gas_expiry_df.parquet')
# # # df.to_parquet('mcx_data/natural_gas_expiry_df.parquet')

# # df = pd.read_parquet('/home/<USER>/repos/data_auditing/arctic_downloads_new/mcx_1min_mcx_fut_trd_7011_20250127_20250212.parquet')

print()
