import pandas as pd
import numpy as np
from arctic.date import DateRange
import logging
import warnings
warnings.filterwarnings("ignore")
from arcticdb import Arctic as Arcticnew
from arctic import Arctic as Arcticold
from pymongo import MongoClient
import logging
import warnings
from arcticc.pb2.descriptors_pb2 import TypeDescriptor
from tqdm import tqdm

TYPE_MAPPING = {
    (TypeDescriptor.UINT, TypeDescriptor.S8): np.uint8,
    (TypeDescriptor.UINT, TypeDescriptor.S16): np.uint16,
    (TypeDescriptor.UINT, TypeDescriptor.S32): np.uint32,
    (TypeDescriptor.UINT, TypeDescriptor.S64): np.uint64,
    (TypeDescriptor.INT, TypeDescriptor.S8): np.int8,
    (TypeDescriptor.INT, TypeDescriptor.S16): np.int16,
    (TypeDescriptor.INT, TypeDescriptor.S32): np.int32,
    (TypeDescriptor.INT, TypeDescriptor.S64): np.int64,
    (TypeDescriptor.FLOAT, TypeDescriptor.S32): np.float32,
    (TypeDescriptor.FLOAT, TypeDescriptor.S64): np.float64,
    (TypeDescriptor.BOOL, TypeDescriptor.S8): bool,
    (TypeDescriptor.INT, TypeDescriptor.UNKNOWN_SIZE_BITS): np.int0,
    (TypeDescriptor.DYNAMIC_STRING, TypeDescriptor.S64): str,
    (TypeDescriptor.NANOSECONDS_UTC, TypeDescriptor.S64): "datetime64[ns]",
}

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

MONGO_CLIENT_IP = "*************"
MONGO_CLIENT_PORT = "27016"
MONGO_CLIENT_USERNAME = "kiviArcticReader"
MONGO_CLIENT_PASSWORD = "kivi"
mongo_connection_path = (
    "mongodb://"
    + MONGO_CLIENT_IP
    + ":"
    + MONGO_CLIENT_PORT
    + "/"
)

# Cutoff date for hybrid copy approach
CUTOFF_DATE = "2023-03-08"

class CopyScript:
    def __init__(self, mongo_client, arctic_client):
        self.mongo_client = mongo_client
        self.arctic_client = arctic_client
        self.store_old = Arcticold(self.mongo_client)
        self.store_new = Arcticnew(self.arctic_client)

    def __sort_index(self, df: pd.DataFrame) -> pd.DataFrame:
        if not df.index.is_monotonic_increasing:
            df.sort_index(inplace=True)
        return df

    def __extract_dtype(self, description):
        """Returns column dtype map from arctic description

        Args:
            description (list): List of tuples of column name and dtype object

        Returns:
            dict: Column dtype map
        """
        col_dtype_map = {}
        for (col, dtype_obj) in description:
            # Extract `value_type` and `size_bits`
            value_type = dtype_obj.value_type
            size_bits = dtype_obj.size_bits
            if (value_type, size_bits) not in TYPE_MAPPING:
                raise ValueError(f"value_type:{TypeDescriptor.ValueType.Name(value_type)}, size_bits:{TypeDescriptor.SizeBits.Name(size_bits)} Not found")
            pandas_dtype = TYPE_MAPPING.get((value_type, size_bits))
            col_dtype_map[col] = pandas_dtype
        return col_dtype_map

    def get_column_dtype_dict(self, lib, symbol):
        return self.__extract_dtype(lib.get_description(symbol).columns)

    def get_index_dtype_dict(self, lib, symbol):
        description = lib.get_description(symbol).index
        return self.__extract_dtype(list(zip(description.name, description.dtype)))

    def update_dtype_of_columns(self, df, index_dtype_dict=None, column_dtype_dict=None):
        if not column_dtype_dict:
            column_dtype_dict = {}
        if not index_dtype_dict:
            index_dtype_dict = {}
        column_dtype_dict.update(index_dtype_dict)
        index_columns = list(df.index.names)
        if index_dtype_dict:
            df = df.reset_index()
        df = df.astype(column_dtype_dict)
        if index_dtype_dict:
            df.set_index(index_columns, inplace=True)
        return df
    
    def pre_process(self, lib, symbol: str, df: pd.DataFrame):
        """Preprocesses dataframe before appending to arcticdb

        Args:
            lib (VersionStore): Arctic library instance
            symbol (str): Symbol to be appended
            df (pd.DataFrame): Dataframe to be appended

        Returns:
            pd.DataFrame: Preprocessed dataframe
        """
        column_names = df.columns
        df.columns = [str(col) for col in df.columns]
        column_dtype_dict = self.get_column_dtype_dict(lib, symbol)
        index_dtype_dict =  self.get_index_dtype_dict(lib, symbol)
        df = self.update_dtype_of_columns(df, index_dtype_dict, column_dtype_dict)
        df.columns = column_names
        df = self.__sort_index(df)
        return df
    
    def copy_symbol(self, source_library: str, target_library, start_date=None, end_date=None, symbol="", _all=False):
        """Copies data from old arctic to new arctic

        Args:
            source_library (str): Source library in old arctic
            target_library (str): Target library in new arctic
            start_date (str, optional): Start date for copying data. Defaults to None.
            end_date (str, optional): End date for copying data. Defaults to None.
            symbol (str, optional): Symbol to be copied. Defaults to "".
            _all (bool, optional): If True, copies entire data. Otherwise, copies data between start_date and end_date. Defaults to False.
        """
        if _all:
            data = self.store_old[source_library].read(symbol)
            self.store_new[target_library].write(symbol, data.data, metadata=data.metadata)
            if source_library.startswith("us/") or source_library.startswith("international/"):
                tail = self.store_new[target_library].tail(symbol, 5).data
                metadata = {"last_timestamp": str(tail.index.get_level_values(0)[-1])}
                self.store_new[target_library].write_metadata(symbol, metadata)
            return
        metadata = self.store_old[source_library].read_metadata(symbol).metadata
        data = self.store_old[source_library].read(symbol, date_range=DateRange(start_date, end_date)).data
        if data.empty:
            return
        if self.store_new[target_library].has_symbol(symbol):
            data = self.pre_process(self.store_new[target_library], symbol, data)
        start_date = data.index.get_level_values(0)[0]
        end_date = data.index.get_level_values(0)[-1]
        
        if self.store_new[target_library].has_symbol(symbol):
            self.store_new[target_library].update(symbol, data, metadata=metadata, date_range=DateRange(start_date, end_date))
        else:
            self.store_new[target_library].write(symbol, data, metadata=metadata)
        if source_library.startswith("us/") or source_library.startswith("international/"):
            tail = self.store_new[target_library].tail(symbol, 5).data
            metadata = {"last_timestamp": str(tail.index.get_level_values(0)[-1])}
            self.store_new[target_library].write_metadata(symbol, metadata)
    
    def copy_library(self, source_library, target_library, start_date=None, end_date=None, _all=False):
        symbols = self.store_old[source_library].list_symbols()
        for symbol in tqdm(symbols):
            try:
                self.copy_symbol(source_library, target_library, start_date, end_date, symbol, _all)
            except Exception as e:
                print(f"Error copying symbol {symbol}: {repr(e)}")
                continue

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Copy data from old Arctic to new Arctic')
    # parser.add_argument('--source', type=str, nargs="+", required=True,
    #                     help="Source library/libraries in old Arctic (space separated for multiple)")
    # parser.add_argument('--mode', type=str, choices=['all', 'cutoff'], default='cutoff',
    #                     help='Copy mode: all or with cutoff date')
    # parser.add_argument('--symbol', type=str, help='Specific symbol to copy. If not provided, copies all symbols.')
    # parser.add_argument('--start-date', type=str, default=CUTOFF_DATE,
    #                     help='Cutoff date for hybrid copy (default: 2023-04-01)')
    # parser.add_argument('--end-date', type=str, help='End date for copying data')
    
    args = parser.parse_args()
    args.source = ["mcx/1_min/mcx_fut_near/column_bucket" ,"mcx/1_min/optcom/column_bucket", "mcx/5_min/mcx_fut_near/column_bucket" ,"mcx/5_min/optcom/column_bucket"]
    args.mode = "cutoff"
    args.start_date = CUTOFF_DATE
    args.symbol=None
    args.end_date=None
    mongo_client = MongoClient(
        mongo_connection_path,
        username=MONGO_CLIENT_USERNAME,
        password=MONGO_CLIENT_PASSWORD,
    )
    arctic_client = "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"

    obj = CopyScript(mongo_client, arctic_client)
    _all = args.mode == "all"
    for source_library in args.source:
        try:
            splits = source_library.split("/")
            splits[0]+="_old_v2"
            target= "/".join(splits)
            if args.symbol:
                obj.copy_symbol(source_library, target, args.start_date, args.end_date, args.symbol, _all)
            else:
                obj.copy_library(source_library, target, args.start_date, args.end_date, _all)
        except Exception as e:
            print(f"Error copying library {source_library}: {repr(e)}")
            continue