import datetime
import calendar
from functools import partial
import numpy as np
import pandas as pd
import os
import time
from multiprocessing import Pool, Manager
import pickle
from io import BytesIO
from minio import Minio


def get_symbol_to_balte_id():
    # get from balte and save it
    with open("/home/<USER>/jupyter/symbol_to_balte_id", "rb") as f:
        return pickle.load(f)


base_path_213 = "/mnt/companydata/MarketData/eq/tick/root_trd"
starting_balte_id = 1
ending_balte_id = 5000
cash_type = "XX"
symbol_to_balte_id = get_symbol_to_balte_id()
errors_on_the_fly_shared_dict = {}


MINIO_END_POINT_219 = "*************:11009"
MINIO_ACCESS_KEY = "minioreader"
MINIO_SECRET_KEY = "reasersecret"

minio_client_143 = Minio(
    MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
)

ALL_DATES = np.load(
    BytesIO(
        minio_client_143.get_object("commondata", "balte_uploads/ALL_DATES.npy").data
    ),
    allow_pickle=True,
)

# # dates_list = os.listdir(base_path_213)
# valid_dates_list = []
# # invalid_dates_list = []

# # for date in dates_list:
# #     if len(date) == 8 and date.isdigit():
# #         valid_dates_list.append(date)
# #     else:
# #         invalid_dates_list.append(date)

# start_date = pd.Timestamp('2024-03-13')
# end_date = pd.Timestamp('2024-09-01')
# valid_dates_list = [date.strftime('%d%m%Y') for date in ALL_DATES if start_date <= date <= end_date]
# print("\nGot date list...\n")

# /mnt/companydata/MarketData/eq/tick/root_trd/01072022/ZEEL/XX/XX/ZEEL.trd

# def get_sym_paths(date, shared_list):
#     path1 = f"{base_path_213}/{date}"
#     try:
#         for sym in os.listdir(path1):
#             if (sym not in symbol_to_balte_id) or (symbol_to_balte_id[sym] >= starting_balte_id and symbol_to_balte_id[sym] <= ending_balte_id):
#                 shared_list.append(f"{base_path_213}/{date}/{sym}/{cash_type}/{cash_type}/{sym}.trd")
#     except Exception as e:
#         print(f"Failed for {date} due to: {e}\n")
#         return

# shared_list = Manager().list()

# print("Started getting all paths...\n")
# with Pool() as pool:
#     pool.starmap(get_sym_paths, [(date, shared_list) for date in valid_dates_list])
# print("Cpmpleted getting all paths\n")

# all_paths = list(shared_list)
# print("Storing all_paths list...\n")
# with open('cash_trd_all_paths_20240313_20240901', 'wb') as f:
#     pickle.dump(all_paths, f)
# print("Storing all_paths list done\n")


def _get_raw_trd_data(path: str):
    try:
        path_split = path.split("/")[1:]
        date_str = path_split[6]
        sym = path_split[-1][:-4]

        tick_data_csv = pd.read_csv(path, header=None)
        tick_data_csv = tick_data_csv[(tick_data_csv > 0).all(axis=1)]

        tick_data_csv["timestamp"] = pd.to_datetime(
            tick_data_csv[0],
            unit="s",
            origin=pd.Timestamp(
                day=int(date_str[0:2]),
                month=int(date_str[2:4]),
                year=int(date_str[4:]),
            ),
        )
        tick_data_csv = tick_data_csv.set_index("timestamp")

        tick_data_csv = tick_data_csv.rename(
            columns={0: "time_in_seconds_count", 2: "ltp", 1: "Volume", 3: "Vwap"}
        )
        tick_data_csv["Volume"] = tick_data_csv["Volume"].astype("float64")

        tick_data_csv["ltp"] = tick_data_csv["ltp"] / 100
        tick_data_csv["Vwap"] = tick_data_csv["Vwap"] / 100

        tick_data_csv = tick_data_csv.resample("1T", closed="right", label="right").agg(
            {"ltp": ["first", "max", "min", "last"], "Volume": "sum", "Vwap": "last"}
        )[["ltp", "Volume", "Vwap"]]

        tick_data_csv = pd.concat(
            [tick_data_csv["ltp"], tick_data_csv["Volume"], tick_data_csv["Vwap"]],
            axis=1,
        )

        tick_data_csv = tick_data_csv.dropna()

        tick_data_csv.columns = ["Open", "High", "Low", "Close", "Volume", "Vwap"]
        tick_data_csv["ID"] = sym
        tick_data_csv = tick_data_csv[
            ["ID", "Open", "High", "Low", "Close", "Vwap", "Volume"]
        ]

        if not os.path.exists(
            f"/home/<USER>/repos/data_auditing/raw_cash_20240313_20240901/{sym}/"
        ):
            os.makedirs(
                f"/home/<USER>/repos/data_auditing/raw_cash_20240313_20240901/{sym}/"
            )

        tick_data_csv.to_parquet(
            f"/home/<USER>/repos/data_auditing/raw_cash_20240313_20240901/{sym}/{date_str}.parquet"
        )
    except Exception as e:
        errors_on_the_fly_shared_dict[path] = str(e)


# _get_raw_trd_data(path='/mnt/companydata/MarketData/eq/tick/root_trd/01012018/ACC/XX/XX/ACC.trd')
# rem_paths_213 = []

# print(f"Loading cash_trd_all_paths_20240313_20240901 list...\n")
# with open("/home/<USER>/repos/data_auditing/cash_trd_all_paths_20240313_20240901", "rb") as f:
#     rem_paths_213 = pickle.load(f)
# print(f"Loading cash_trd_all_paths_20240313_20240901 list completed\n")


# start_time = time.time()
# print(f"Started getting data from paths at {pd.Timestamp.now()}...\n")

# with Pool() as pool:
#     pool.map(_get_raw_trd_data, rem_paths_213)

# end_time = time.time()
# print(f"Completed getting data for {len(rem_paths_213)} paths in {end_time - start_time} seconds\n")


# print("Dumping errors on the fly...")
# with open(
#     "/home/<USER>/repos/data_auditing/errors_on_the_fly_for_getting_raw_cash_from_tick_for_cash_trd_all_paths_20240313_20240901",
#     "wb",
# ) as f:
#     pickle.dump(dict(errors_on_the_fly_shared_dict), f)
# print("Dumping completed errors on the fly dict\n")


## merging into one parquet

# base_path = "/home/<USER>/repos/data_auditing/raw_cash_20240313_20240901"

# print(f"Started combining data files at {pd.Timestamp.now()}...\n")
# start_time = time.time()
# count = 0

# for sym in os.listdir(base_path):
#     path1 = f"{base_path}/{sym}"
#     data_list = []

#     for date in os.listdir(path1):
#         count += 1
#         data_list.append(pd.read_parquet(f"{path1}/{date}"))

#     data = pd.concat(data_list)
#     data = data.sort_index()

#     data.to_parquet(f"{path1}/combined.parquet")

#     for date in os.listdir(path1):
#         if 'combined' in date: continue
#         os.remove(f'{path1}/{date}')

# end_time = time.time()
# print(f"Completed combining {count} data files in {end_time - start_time} seconds\n")


# import pandas as pd
# import os
# from arcticdb import Arctic

# store = Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")

# # library = "nse/1_min/raw_cash/trd"
# # lib = store[library]
# # symbols = lib.list_symbols()

# # base_path = "/home/<USER>/repos/data_auditing/raw_cash"
# # for sym in os.listdir(base_path):
# #     df = pd.read_parquet(f"{base_path}/{sym}/combined.parquet")
# #     if sym in symbols:
# #         dfal=lib.read(sym).data
# #         df=pd.concat([df,dfal])
# #         df=df.sort_index()
# #     lib.write(sym, df)

print()
