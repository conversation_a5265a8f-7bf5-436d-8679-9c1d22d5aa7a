import pickle
import pandas as pd
from arcticdb import Arctic
import datetime
from minio import Minio
from main.tanki import Tanki


def fix_is_list(islist):
    storek = Arctic(
        "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
    )
    tanki = Tanki(exchange_type="nse", write=True)
    tanki.login(username="mantraraj", password="mantraraj")

    minio_client = Minio(
        "192.168.0.198:11009", "minioreader", "reasersecret", secure=False
    )

    demerger_merger = (
        storek["nse/1440_min/after_market/trd"].read("demerger_merger").data
    )
    symbol_change = storek["nse/1440_min/after_market/trd"].read("symbol_change").data
    symbol_change = symbol_change.reset_index()

    old_islist = storek["nse/1440_min/after_market/trd"].read(islist).data
    old_cols = old_islist.columns
    old_dtypes = old_islist.dtypes
    old_islist.to_parquet(
        f"/home/<USER>/repos/data_auditing/backup_cash_segment_fix_20250723/after_market_backup/{islist}.parquet"
    )

    symbol_to_balte_id = pickle.loads(
        minio_client.get_object("commondata", "balte_uploads/mapping_dict").data
    )

    for _, row in symbol_change.iterrows():
        if (
            row["symbol"] in symbol_to_balte_id
            and row["current"] not in symbol_to_balte_id
        ):
            symbol_to_balte_id[row["current"]] = symbol_to_balte_id[row["symbol"]]
        if (
            row["current"] in symbol_to_balte_id
            and row["symbol"] not in symbol_to_balte_id
        ):
            symbol_to_balte_id[row["symbol"]] = symbol_to_balte_id[row["current"]]

    balte_id_to_symbol = {}
    for k, v in symbol_to_balte_id.items():
        balte_id_to_symbol[v] = k

    # create mapping from ID to symbol from current balte_id_to_symbol
    old_islist["symbol"] = old_islist["ID"].map(balte_id_to_symbol)
    
    if len(old_islist[old_islist.symbol.isna()]):
        raise

    # replacing symbol name for e.g. PEL_04OCT2002 to PEL
    old_islist["symbol"] = old_islist["symbol"].str.replace(
        r"_\d{2}[A-Za-z]{3}\d{4}$", "", regex=True
    )
    old_islist = old_islist.reset_index()

    # we will use symbol change to get current symbol for some symbol having IDs changed with symbol also
    # merging islist with symbol change data on symbol
    merged_df = pd.merge(old_islist, symbol_change, on="symbol", how="left")
    # if mapping exist of symbol to some another current symbol then make it as current symbol
    merged_df["symbol"] = merged_df["current"].combine_first(merged_df["symbol"])
    merged_df = merged_df.drop(columns=["date_y", "current", "ID"])
    merged_df = merged_df.rename(columns={"date_x": "date"}).set_index("date")
    merged_df["ID"] = merged_df["symbol"].map(symbol_to_balte_id)
    
    ## Need to handle manually
    merged_df.loc[merged_df.symbol == "MAXINDIA", "ID"] = 1947
    merged_df.loc[merged_df.symbol == "MEGH", "ID"] = 771
    merged_df.loc[merged_df.symbol == "ORTINLABSS", "ID"] = 1855
    
    if len(merged_df[merged_df.ID.isna()]):
        raise
    
    old_islist = merged_df

    new_islist = old_islist.copy()

    # then again changing ID with demerger_merger info
    for sym, _ in new_islist.groupby("symbol"):
        dmsym = demerger_merger[demerger_merger.Symbol == sym]
        for index in range(len(dmsym) - 1, -1, -1):
            new_islist.loc[
                (new_islist.symbol == sym) & (new_islist.index < dmsym.index[index]),
                "ID",
            ] = dmsym.iloc[index]["ID"]

    new_islist = new_islist[old_cols]
    for col in new_islist.columns:
        new_islist[col] = new_islist[col].astype(old_dtypes[col])

    storek['nse/1440_min/after_market/trd'].delete(islist)
    tanki["nse/1440_min/after_market/trd"].write_metadata(islist, new_islist)
    tanki["nse/1440_min/after_market/trd"].write(islist, new_islist)
    
    print(f"Completed for {islist}\n")


# fix_is_list(islist="isfno")   
# fix_is_list(islist="isht")
# fix_is_list(islist='isliquid')
# fix_is_list(islist='isbanknifty')
# fix_is_list(islist='isnifty')
fix_is_list(islist='iseq')