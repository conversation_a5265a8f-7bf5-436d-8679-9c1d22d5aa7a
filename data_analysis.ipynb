{"cells": [{"cell_type": "code", "execution_count": 2, "id": "6c5ee4d3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from arcticdb import Arctic"]}, {"cell_type": "code", "execution_count": 3, "id": "ddceb505", "metadata": {}, "outputs": [], "source": ["storek=Arctic(f\"s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=pd.read_parquet('/home/<USER>/repos/data_auditing/cash_backup_20250722_without_corpact_of_20250723/1075.parquet')\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.Close.plot()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f54279ac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "data_auditing_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}